# Dependencies
node_modules/
*/node_modules/

# Production builds
dist/
build/
*/dist/
*/build/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*/.env
*/.env.local
*/.env.development.local
*/.env.test.local
*/.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Bun
.bun

# TypeScript
*.tsbuildinfo

# Build outputs
server/public/
server/dist/
client/dist/

# Test outputs
coverage/
test-results/

# Railway
.railway/

# Puppeteer
.puppeteer/

# Local SSL certificates
*.pem
*.key
*.crt

# Package manager lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
# bun.lockb

# CLAUDE
CLAUDE.md
.claude/

# Others
.windsurf/