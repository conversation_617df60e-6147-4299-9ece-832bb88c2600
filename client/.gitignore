# Dependencies
node_modules/

# Production build
dist/
dist-ssr/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build tools
.cache/
.parcel-cache/
.vite/

# TypeScript
*.tsbuildinfo

# ESLint cache
.eslintcache

# Bun
.bun

# Coverage
coverage/
.nyc_output/
