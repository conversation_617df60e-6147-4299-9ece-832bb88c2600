@import "tailwindcss";

/* Override Tailwind's list reset specifically for Quill editor */
.ql-editor li {
  list-style: revert;
}

.ql-editor ul,
.ql-editor ol {
  list-style: revert;
  padding-left: 1.5rem;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Quill Editor List Styling - Fix double bullets and overlapping numbers */
.ql-editor li[data-list="bullet"] {
  list-style: none !important;
  display: list-item !important;
  margin-left: 0 !important;
  padding-left: 1.5rem !important;
  position: relative;
}

.ql-editor li[data-list="bullet"]:before {
  content: "•" !important;
  position: absolute;
  left: 0;
  color: inherit;
}

.ql-editor li[data-list="ordered"] {
  list-style: none !important;
  display: list-item !important;
  margin-left: 0 !important;
  padding-left: 1.5rem !important;
  position: relative;
  counter-increment: list-counter;
}

.ql-editor li[data-list="ordered"]:before {
  content: counter(list-counter) "." !important;
  position: absolute;
  left: 0;
  color: inherit;
}

.ql-editor li {
  margin: 0.25rem 0 !important;
  line-height: 1.5 !important;
}

/* Reset counter for ordered lists */
.ql-editor {
  counter-reset: list-counter;
}

/* Reset counter at the start of each new ordered list */
.ql-editor li[data-list="ordered"]:not([data-list="ordered"] ~ li[data-list="ordered"]) {
  counter-reset: list-counter;
}

/* Ensure counter resets when switching from bullet to ordered */
.ql-editor li[data-list="bullet"] + li[data-list="ordered"] {
  counter-reset: list-counter;
}

/* Reset counter when ordered list starts after non-list content */
.ql-editor p + li[data-list="ordered"],
.ql-editor div + li[data-list="ordered"],
.ql-editor br + li[data-list="ordered"] {
  counter-reset: list-counter;
}

/* Handle nested lists with data-list attributes */
.ql-editor li[data-list="bullet"] li[data-list="bullet"]:before {
  content: "◦" !important;
}

.ql-editor li[data-list="bullet"] li[data-list="bullet"] li[data-list="bullet"]:before {
  content: "▪" !important;
}

/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Quill editor specific styling */
.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

/* Clean up any conflicting list styles */
.ql-editor ul,
.ql-editor ol {
  list-style: none !important;
  padding-left: 0 !important;
  margin: 0.5rem 0 !important;
}

/* Toolbar alignment and styling fixes */
.ql-toolbar {
  border-top: 1px solid #ccc;
  border-left: 1px solid #ccc;
  border-right: 1px solid #ccc;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  padding: 8px;
}

.ql-toolbar .ql-formats {
  margin-right: 15px;
  display: inline-block;
  vertical-align: top;
}

.ql-toolbar button {
  background: none;
  border: none;
  cursor: pointer;
  display: inline-block;
  float: left;
  height: 24px;
  padding: 3px 5px;
  width: 28px;
  text-align: center;
  vertical-align: middle;
}

.ql-toolbar button:hover {
  color: #06c;
}

.ql-toolbar button.ql-active {
  color: #06c;
}

/* Specific list button styling */
.ql-toolbar .ql-list {
  width: 28px !important;
}

.ql-toolbar .ql-list.ql-active {
  color: #06c;
}

/* Additional container styling */
.ql-container {
  box-sizing: border-box;
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-size: 13px;
  height: 100%;
  margin: 0px;
  position: relative;
}
