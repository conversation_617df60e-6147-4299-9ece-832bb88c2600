@import "tailwindcss";

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Quill Editor and Preview List Styling */
.ql-editor ul,
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ql-editor ol,
.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ql-editor li,
.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.ql-editor ul ul,
.prose ul ul {
  list-style-type: circle;
}

.ql-editor ul ul ul,
.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Quill editor specific styling */
.ql-container {
  font-size: 14px;
}

.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

/* Ensure Quill toolbar icons are properly styled */
.ql-toolbar .ql-formats {
  margin-right: 15px;
}

.ql-toolbar button {
  padding: 5px;
  margin: 0 2px;
}

.ql-toolbar button.ql-active {
  color: #06c;
}
