@import "tailwindcss";

/* Override Tailwind's list reset specifically for Quill editor */
.ql-editor li {
  list-style: revert;
}

.ql-editor ul,
.ql-editor ol {
  list-style: revert;
  padding-left: 1.5rem;
}

/* Smooth scrolling for all scrollable elements */
* {
  scroll-behavior: smooth;
}

/* Ensure proper scrolling in preview containers */
.preview-container {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.preview-container::-webkit-scrollbar {
  width: 6px;
}

.preview-container::-webkit-scrollbar-track {
  background: transparent;
}

.preview-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Quill Editor List Styling - Handle Quill's data-list attributes */
.ql-editor li[data-list="bullet"] {
  list-style-type: disc !important;
  display: list-item !important;
  margin-left: 1.5rem !important;
  padding-left: 0 !important;
}

.ql-editor li[data-list="ordered"] {
  list-style-type: decimal !important;
  display: list-item !important;
  margin-left: 1.5rem !important;
  padding-left: 0 !important;
}

.ql-editor li {
  margin: 0.25rem 0 !important;
  line-height: 1.5 !important;
}

/* Handle nested lists with data-list attributes */
.ql-editor li[data-list="bullet"] li[data-list="bullet"] {
  list-style-type: circle !important;
}

.ql-editor li[data-list="bullet"] li[data-list="bullet"] li[data-list="bullet"] {
  list-style-type: square !important;
}

/* Fallback for standard HTML lists in Quill (if any) */
.ql-editor ul {
  list-style-type: disc !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.ql-editor ol {
  list-style-type: decimal !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

/* Preview styling (without !important as these shouldn't conflict) */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.prose li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.prose ul ul {
  list-style-type: circle;
}

.prose ul ul ul {
  list-style-type: square;
}

/* Ensure list styles are preserved in all preview contexts */
[dangerouslySetInnerHTML] ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

[dangerouslySetInnerHTML] li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

/* Quill editor specific styling */
.ql-container {
  font-size: 14px;
}

.ql-editor.ql-blank::before {
  font-style: italic;
  color: #aaa;
}

/* Override Tailwind's list-style reset for Quill editor */
.ql-editor ul,
.ql-editor ol {
  list-style: revert !important;
  padding-left: 1.5rem !important;
}

.ql-editor ul {
  list-style-type: disc !important;
}

.ql-editor ol {
  list-style-type: decimal !important;
}

/* Ensure list items display correctly in Quill */
.ql-editor li {
  display: list-item !important;
  margin-left: 0 !important;
  padding-left: 0 !important;
}

/* Additional Quill data-list attribute handling */
.ql-editor [data-list="bullet"] {
  list-style-type: disc !important;
  display: list-item !important;
}

.ql-editor [data-list="ordered"] {
  list-style-type: decimal !important;
  display: list-item !important;
}

/* Ensure proper counter reset for ordered lists */
.ql-editor ol,
.ql-editor li[data-list="ordered"]:first-of-type {
  counter-reset: list-counter;
}

/* Override any Tailwind CSS reset that might interfere */
.ql-editor li[data-list] {
  list-style: revert !important;
}

/* Force proper list display in Quill editor */
.ql-editor li[data-list="bullet"]::marker {
  content: "• ";
}

.ql-editor li[data-list="ordered"] {
  counter-increment: list-counter;
}

.ql-editor li[data-list="ordered"]::marker {
  content: counter(list-counter) ". ";
}

/* Ensure proper spacing and indentation */
.ql-editor li[data-list] {
  margin-left: 1.5rem !important;
  padding-left: 0.25rem !important;
}

/* Critical: Override Quill's default list counter behavior */
.ql-editor {
  counter-reset: list-0 list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}

.ql-editor li[data-list="bullet"] {
  counter-reset: none !important;
  counter-increment: none !important;
}

.ql-editor li[data-list="ordered"] {
  counter-increment: list-0 !important;
}

/* Ensure bullets show correctly */
.ql-editor li[data-list="bullet"]:before {
  content: none !important;
}

.ql-editor li[data-list="ordered"]:before {
  content: none !important;
}

/* Ensure Quill toolbar icons are properly styled */
.ql-toolbar .ql-formats {
  margin-right: 15px;
}

.ql-toolbar button {
  padding: 5px;
  margin: 0 2px;
}

.ql-toolbar button.ql-active {
  color: #06c;
}

/* Specific styling for Quill list buttons */
.ql-toolbar .ql-list[value="bullet"]::before {
  content: "•";
}

.ql-toolbar .ql-list[value="ordered"]::before {
  content: "1.";
}
