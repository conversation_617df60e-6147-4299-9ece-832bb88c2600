import React, { useState } from 'react';
import { Download, Loader2 } from 'lucide-react';
import { useCVStore } from '../../store/cvStore';

interface DownloadPdfButtonProps {
  className?: string;
  id?: string;
  iconOnly?: boolean;
}

const DownloadPdfButton: React.FC<DownloadPdfButtonProps> = ({ className, id, iconOnly = false }): React.ReactElement => {
  const { data, visibility, selectedTemplate } = useCVStore();
  const [isLoading, setIsLoading] = useState(false);
  
  const hasData = Object.values(data).some(
    (value) => 
      (typeof value === 'string' && value.length > 0) || 
      (Array.isArray(value) && value.length > 0) ||
      (typeof value === 'object' && value !== null && Object.values(value).some(v => Boolean(v) && typeof v === 'string' && v.length > 0))
  );

  const handleDownload = async () => {
    try {
      setIsLoading(true);
      
      // Call server API endpoint (relative path since it's served from the same server)
      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cvData: data, visibility, templateName: selectedTemplate }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }
      
      // Get the blob from the response
      const blob = await response.blob();
      
      // Create a URL for the blob
      const url = URL.createObjectURL(blob);
      
      // Create a download link and trigger it
      const link = document.createElement('a');
      link.href = url;
      link.download = `${data.personalInfo?.firstName || 'CV'}_${data.personalInfo?.lastName || 'Resume'}.pdf`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!hasData) {
    return (
      <button 
        className={className}
        disabled
        title="Add some content to your CV first"
        id={id}
        type="button"
        aria-label="Download PDF (disabled)"
      >
        <Download className={iconOnly ? "w-5 h-5" : "w-4 h-4 mr-2"} aria-hidden="true" />
        {!iconOnly && "Download PDF"}
      </button>
    );
  }

  return (
    <button
      type="button"
      onClick={handleDownload}
      className={className}
      id={id}
      aria-label="Download PDF"
      title={iconOnly ? "Download PDF" : undefined}
    >
      {isLoading ? (
        <Loader2 className={iconOnly ? "w-5 h-5 animate-spin" : "w-4 h-4 mr-2 animate-spin"} aria-hidden="true" />
      ) : (
        <Download className={iconOnly ? "w-5 h-5" : "w-4 h-4 mr-2"} aria-hidden="true" />
      )}
      {!iconOnly && (isLoading ? 'Generating...' : 'Download PDF')}
    </button>
  );
};

export default DownloadPdfButton;
