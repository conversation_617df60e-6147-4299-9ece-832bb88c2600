# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/

# Build output
dist/
public/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Coverage
coverage/
.nyc_output/

# IDE files
.idea
.vscode
*.sublime-project
*.sublime-workspace

# OS files
.DS_Store
Thumbs.db

# Bun
.bun

# TypeScript
*.tsbuildinfo

# Puppeteer
.puppeteer/

# Test outputs
test-results/

# Railway
.railway/
